// ignore_for_file: unnecessary_string_interpolations

import 'package:bibl/controllers/auth_controller.dart';
import 'package:bibl/models/league_user_model.dart';
import 'package:bibl/res/style.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:cloud_functions/cloud_functions.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'controllers/profile_controller.dart';
import 'controllers/leaderboard_controller.dart';
import 'services/league_service.dart';
import 'widgets/customappbar.dart';
import 'widgets/leaderboard_table.dart';
import 'widgets/league_clock_widget.dart';

class Leaderboard extends StatefulWidget {
  const Leaderboard({super.key});

  @override
  State<Leaderboard> createState() => _LeaderboardState();
}

class _LeaderboardState extends State<Leaderboard> {
  final ProfileController profileController = Get.find();
  final LeaderboardController leaderboardController = Get.find();
  final LeagueService leagueService = LeagueService();
  bool _isInitializing = false;
  Future<void> _ensureUserInLeague(String userId, String username) async {
    if (_isInitializing) return;

    setState(() {
      _isInitializing = true;
    });

    try {
      await leagueService.assignUserToBronzana(
        userId,
        username,
        profileController,
      );
    } catch (e) {
      debugPrint('Failed to ensure user in league: $e');
    } finally {
      if (mounted) {
        setState(() {
          _isInitializing = false;
        });
      }
    }
  }

  Future<void> _addUserToCurrentLeague(
      String league, String groupId, String userId, String username) async {
    try {
      await leagueService.addUserToLeague(
        league,
        groupId,
        userId,
        username,
        profileController,
      );
    } catch (e) {
      debugPrint('Failed to add user to current league: $e');
    }
  }

  @override
  Widget build(BuildContext context) {
    final user = FirebaseAuth.instance.currentUser;

    if (user == null) {
      return _buildErrorScaffold('Please log in to view the leaderboard.');
    }

    return SafeArea(
      top: false,
      child: Scaffold(
        backgroundColor: Colors.white,
        appBar: CustomAppBar(
          isBackButton: false,
          widget: null,
          title: 'umniLab lige',
        ),
        body: _buildUserDataStream(user.uid),
      ),
    );
  }

  Widget _buildErrorScaffold(String message) {
    return Scaffold(
      body: Center(child: Text(message)),
    );
  }

  Widget _buildUserDataStream(String userID) {
    return StreamBuilder<DocumentSnapshot>(
      stream: FirebaseFirestore.instance
          .collection('flags')
          .doc('bvJSDVy763Blf5iJxeLQ')
          .snapshots(),
      builder: (context, flagSnapshot) {
        // Check if leaderboard is updating
        if (flagSnapshot.hasData && flagSnapshot.data!.exists) {
          final flagData = flagSnapshot.data!.data() as Map<String, dynamic>;
          final isLeaderboardUpdating =
              flagData['isLeaderboardUpdating'] ?? false;

          if (isLeaderboardUpdating) {
            return const Center(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  CircularProgressIndicator(color: mainColor),
                  SizedBox(height: 16),
                  Text(
                    'Leaderboard is updating...',
                    style: TextStyle(fontSize: 16, fontWeight: FontWeight.w500),
                  ),
                  SizedBox(height: 8),
                  Text(
                    'Please wait while we update the leagues',
                    style: TextStyle(fontSize: 14, color: Colors.grey),
                  ),
                ],
              ),
            );
          }
        }

        // If not updating, show normal leaderboard
        return StreamBuilder<DocumentSnapshot>(
          stream: FirebaseFirestore.instance
              .collection('users')
              .doc(userID)
              .snapshots(),
          builder: (context, snapshot) {
            if (snapshot.connectionState == ConnectionState.waiting ||
                _isInitializing) {
              return const Center(
                child: CircularProgressIndicator(color: mainColor),
              );
            }

            if (snapshot.hasError) {
              return const Center(child: Text('Error loading user data.'));
            }

            if (!snapshot.hasData || !snapshot.data!.exists) {
              return const Center(child: Text('User not found.'));
            }

            final userData = snapshot.data!.data() as Map<String, dynamic>;
            final league = userData['league'] ?? '';
            final groupId = userData['groupId'] ?? '';

            return _handleUserLeagueStatus(league, groupId, userID);
          },
        );
      },
    );
  }

  Widget _handleUserLeagueStatus(String league, String groupId, String userID) {
    if (league.isEmpty || groupId.isEmpty) {
      // Check leaderboard updating flag before auto-adding user
      return StreamBuilder<DocumentSnapshot>(
        stream: FirebaseFirestore.instance
            .collection('flags')
            .doc('bvJSDVy763Blf5iJxeLQ')
            .snapshots(),
        builder: (context, flagSnapshot) {
          bool isLeaderboardUpdating = false;
          if (flagSnapshot.hasData && flagSnapshot.data!.exists) {
            final flagData = flagSnapshot.data!.data() as Map<String, dynamic>;
            isLeaderboardUpdating = flagData['isLeaderboardUpdating'] ?? false;
          }

          if (!isLeaderboardUpdating) {
            WidgetsBinding.instance.addPostFrameCallback((_) async {
              await _ensureUserInLeague(
                userID,
                profileController.userr.value.uniqueName ?? '',
              );
            });
          }

          return const Center(
            child: CircularProgressIndicator(color: mainColor),
          );
        },
      );
    }

    return _buildLeaderboardContent(league, groupId, userID);
  }

  Widget _buildLeaderboardContent(
      String league, String groupId, String userID) {
    return Column(
      children: [
        Expanded(
          child: Stack(
            children: [
              Positioned.fill(
                child: Column(
                  children: [
                    Expanded(
                      child: _buildLeaderboardStream(league, groupId, userID),
                    ),
                  ],
                ),
              ),
              Align(
                alignment: Alignment.bottomCenter,
                child: Container(child: leagueClockWidget()),
              ),
            ],
          ),
        ),
      ],
    );
  }

  Widget _buildLeaderboardStream(String league, String groupId, String userID) {
    return StreamBuilder<QuerySnapshot>(
      stream: leagueService.getLeaderboardStream(league, groupId),
      builder: (context, snapshot) {
        if (snapshot.connectionState == ConnectionState.waiting ||
            snapshot.hasError) {
          return const Center(
            child: CircularProgressIndicator(color: mainColor),
          );
        }

        final players = snapshot.data!.docs
            .map((doc) => LeaguePlayerModel.fromSnap(doc))
            .toList();

        final currentUserId = profileController.userr.value.uid ?? '';
        final isUserInLeague =
            leagueService.isUserInLeague(players, currentUserId);

        if (!isUserInLeague && currentUserId.isNotEmpty) {
          // Check leaderboard updating flag before auto-adding user
          FirebaseFirestore.instance
              .collection('flags')
              .doc('bvJSDVy763Blf5iJxeLQ')
              .get()
              .then((flagDoc) {
            bool isLeaderboardUpdating = false;
            if (flagDoc.exists) {
              final flagData = flagDoc.data() as Map<String, dynamic>;
              isLeaderboardUpdating =
                  flagData['isLeaderboardUpdating'] ?? false;
            }

            if (!isLeaderboardUpdating) {
              WidgetsBinding.instance.addPostFrameCallback((_) async {
                await _addUserToCurrentLeague(
                  league,
                  groupId,
                  currentUserId,
                  profileController.userr.value.uniqueName ?? '',
                );
              });
            }
          });
        }

        if (players.isEmpty) {
          return const Center(
            child: Text('No players found in this group.'),
          );
        }

        return ListView(
          padding: const EdgeInsets.all(16.0),
          children: [
            LeaderboardTable(
              players: players,
              profileController: profileController,
            ),
          ],
        );
      },
    );
  }
}
