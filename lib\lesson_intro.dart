import 'dart:io';

import 'package:bibl/controllers/analytics_controller.dart';
import 'package:bibl/controllers/audio_controller.dart';
import 'package:bibl/controllers/lesson_controller.dart';
import 'package:bibl/controllers/profile_controller.dart';
import 'package:bibl/lesson.dart';
import 'package:bibl/models/lesson_model.dart';
import 'package:bibl/res/style.dart';
import 'package:bibl/services/lessonquiz_completion_service.dart';
import 'package:bibl/widgets/custombutton.dart';
import 'package:bibl/widgets/rewarded_ads_dialog.dart';
import 'package:flutter/material.dart';

import 'package:get/get.dart';

import 'widgets/boxtile_category_image_widget.dart';
import 'widgets/customappbar.dart';
import 'widgets/local_image_widget.dart';

class LessonIntro extends StatefulWidget {
  final LessonModel lesson;

  const LessonIntro({
    super.key,
    required this.lesson,
  });

  @override
  State<LessonIntro> createState() => _LessonIntroState();
}

class _LessonIntroState extends State<LessonIntro> with WidgetsBindingObserver {
  final ProfileController profileController = Get.find();
  final LessonController lessonController = Get.find();
  final AudioController audioController = Get.find();
  final AnalticsController analticsController = AnalticsController();
  bool _isDisposed = false;
  bool isShouldBlackGradient = true;
  audioChecker() async {
    // Preload audio file
    if (widget.lesson.audioLink != null) {
      bool audio = await audioController.preloadAudio(
          widget.lesson.audioLink!, "${widget.lesson.lessonId}_audio.mp3");
      if (audio && !_isDisposed) {
        await audioController.playAudio();
      }
    }
  }

  void _preloadImages() async {
    for (var page in widget.lesson.pages!) {
      await lessonController.fetchAndCacheImage(
          page.pagePhotoLink!, "${widget.lesson.lessonId}_${page.pageNo}.jpg");
    }
  }

  @override
  void initState() {
    super.initState();
    _preloadImages();
    audioChecker();
  }

  @override
  void dispose() {
    _isDisposed = true;
    // Stop audio playback and release resources
    audioController.stopAudio();
    // audioController.player.dispose(); // Ensure player is disposed
    super.dispose();
  }

  bool _isGoingBack = false;

  _goBack() {
    if (_isGoingBack) return;

    setState(() {
      isShouldBlackGradient = false;
    });

    Future.delayed(Duration.zero, () {
      _isGoingBack = true;
      Get.back();
    });
  }

  @override
  Widget build(BuildContext context) {
    var statusBarheight = MediaQuery.of(context).viewPadding.top;
    return PopScope(
        canPop: false,
        onPopInvokedWithResult: (bool didPop, Object? result) async {
          _goBack();
        },
        child: SafeArea(
          bottom: true,
          top: false,
          child: Scaffold(
            appBar: CustomAppBar(
              widget: null,
              title: widget.lesson.lessonName!,
            ),
            body: Stack(
              children: [
                Positioned.fill(
                  child: ScrollConfiguration(
                    behavior: const ScrollBehavior(),
                    child: ListView(
                      children: [
                        Padding(
                          padding: const EdgeInsets.all(16.0),
                          child: Container(
                            decoration: BoxDecoration(
                              border: Border.all(
                                  color: Colors.black.withValues(alpha: 0.2)),
                              borderRadius: BorderRadius.circular(16),
                            ),
                            child: Padding(
                              padding: const EdgeInsets.all(16.0),
                              child: Column(
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: [
                                  Row(
                                    crossAxisAlignment:
                                        CrossAxisAlignment.start,
                                    children: [
                                      boxTileCategoryImageBuilder(
                                          widget.lesson.category!),
                                      const SizedBox(width: 16.0),
                                      Expanded(
                                        child: Column(
                                          crossAxisAlignment:
                                              CrossAxisAlignment.start,
                                          children: [
                                            Txt(
                                              txt: widget.lesson.lessonName!,
                                              maxLines: 3,
                                              fontSize: 14,
                                            ),
                                            Txt(
                                              txt: widget.lesson.category!,
                                              fontSize: 14,
                                              fontWeight: FontWeight.normal,
                                              fontColor: grey2Color,
                                            ),
                                          ],
                                        ),
                                      ),
                                    ],
                                  ),
                                  const SizedBox(height: 16.0),
                                  imageWidget(statusBarheight),
                                  const SizedBox(height: 16.0),
                                  Txt(
                                    txt: widget.lesson.intro!,
                                    maxLines: 1000,
                                    fontColor: grey2Color,
                                    fontSize: 14,
                                    fontWeight: FontWeight.normal,
                                  ),
                                ],
                              ),
                            ),
                          ),
                        ),
                        const SizedBox(height: 100.0),
                      ],
                    ),
                  ),
                ),
                Align(
                    alignment: Alignment.bottomCenter,
                    child: Padding(
                      padding: const EdgeInsets.all(16.0),
                      child: buttonContainer(
                        text: 'Krenimo',
                        onTap: () {
                          bool isLessonCompleted = profileController
                                  .userr.value.completedLessonQuizesInThirtyDays
                                  ?.containsKey(widget.lesson.lessonId) ??
                              false;

                          if (!profileController.userr.value.isPremiumUser!) {
                            print('lesssonnnnnnnnnnnn: its not premium');
                            final completionService =
                                LessonQuizCompletionService();
                            bool isOpenedWithin30Days =
                                completionService.isOpenedWithinThirtyDays(
                                    widget.lesson.lessonId!,
                                    profileController.userr.value
                                        .openedQuizesAndArticlesinMonth);

                            if (isOpenedWithin30Days || isLessonCompleted) {
                              Get.to(
                                () => Lesson(lesson: widget.lesson),
                              );
                            } else {
                              if (profileController.userr.value.hearts == 0) {
                                Get.dialog(RewardedAdsDialog(
                                  classs: Lesson(lesson: widget.lesson),
                                ));
                              } else {
                                Get.to(
                                  () => Lesson(lesson: widget.lesson),
                                );
                                profileController.userr.value.hearts =
                                    profileController.userr.value.hearts! - 1;
                                profileController.updateHearts(
                                    profileController.userr.value.hearts!);
                              }
                            }
                            profileController.userr.refresh();
                          } else {
                            Get.to(
                              () => Lesson(lesson: widget.lesson),
                            );
                          }
                        },
                      ),
                    )),
              ],
            ),
          ),
        ));
  }

  SizedBox imageWidget(double statusBarheight) {
    return SizedBox(
      width: double.infinity,
      child: Stack(
        children: [
          AspectRatio(
            aspectRatio: 16 / 9,
            child: FutureBuilder<File?>(
              future: lessonController.fetchAndCacheImage(
                  widget.lesson.imageLink!,
                  "${widget.lesson.lessonId}_lesson_image.jpg"),
              builder: (context, snapshot) {
                return LocalImageWidget(imageFile: snapshot.data);
              },
            ),
          ),
          Positioned(
            bottom: 10,
            right: 10,
            child: CircleAvatar(
              backgroundColor: Colors.black.withValues(alpha: 0.2),
              child: Obx(
                () => IconButton(
                  onPressed: () async {
                    bool newSoundStatus = !profileController.isSoundOn.value;

                    // Toggle sound status
                    profileController.setSoundStatus(newSoundStatus);

                    if (newSoundStatus) {
                      // Replay audio if sound is turned on
                      await audioController.playAudio();
                    } else {
                      // Stop audio if sound is turned off
                      await audioController.stopAudio();
                    }
                  },
                  icon: profileController.isSoundOn.value
                      ? const Icon(
                          Icons.volume_up_outlined,
                          color: Colors.white,
                        )
                      : const Icon(
                          Icons.volume_off_outlined,
                          color: Colors.white,
                        ),
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }
}
