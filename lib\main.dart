import 'package:bibl/controllers/leaderboard_controller.dart';
import 'package:bibl/routes/app_routes.dart';
import 'package:bibl/services/ad_mob_service.dart';
import 'package:bibl/services/premium_ad_manager.dart';

import 'package:figma_to_flutter/figma_to_flutter.dart';
import 'package:firebase_core/firebase_core.dart';
import 'package:firebase_messaging/firebase_messaging.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_dotenv/flutter_dotenv.dart';
import 'package:get/get.dart';
import 'package:google_mobile_ads/google_mobile_ads.dart';
import 'controllers/audio_controller.dart';
import 'controllers/auth_controller.dart';
import 'controllers/heart_controller.dart';
import 'controllers/category_controller.dart';
import 'controllers/lesson_controller.dart';
import 'controllers/quiz_controller.dart';
import 'controllers/profile_controller.dart';
import 'utils/comprehensive_performance_manager.dart';

import 'res/style.dart';
import 'services/notification_service.dart';

import 'package:timezone/data/latest_all.dart' as tz;

import 'services/shared_preferences.dart';

// Define a background message handler for handling notifications when the app is in the background or terminated
Future<void> _firebaseMessagingBackgroundHandler(RemoteMessage message) async {
  await Firebase.initializeApp();
  // print("Handling a background message: ${message.messageId}");
}

// Preload fonts to prevent text jumping
Future<void> _preloadFonts() async {
  try {
    // Preload all font variations to prevent FOUC (Flash of Unstyled Content)
    final fontLoader = FontLoader('SF Pro Text');
    fontLoader.addFont(rootBundle.load('assets/fonts/SF-Pro-Text-Bold.otf'));
    await fontLoader.load();

    // Additional font preloading for Poppins if needed
    await Future.delayed(const Duration(milliseconds: 50));
    debugPrint('✅ Fonts preloaded successfully');
  } catch (e) {
    debugPrint('❌ Error preloading fonts: $e');
  }
}

// void getToken() async {
//   FirebaseMessaging messaging = FirebaseMessaging.instance;

//   try {
//     String? token = await messaging.getToken();
//     if (token != null) {
//       print("FCM Token: $token");
//       // You can use the token here, for example, show it in a toast
//     } else {
//       print("Failed to fetch FCM Token");
//     }
//   } catch (e) {
//     print("Error fetching FCM Token: $e");
//   }
// }

Future<void> main() async {
  await dotenv.load(fileName: ".env");
  WidgetsFlutterBinding.ensureInitialized();

  // Configure Flutter for optimal performance
  if (kReleaseMode) {
    debugPrint = (String? message, {int? wrapWidth}) {};
  }

  // Initialize comprehensive performance manager
  final performanceManager = ComprehensivePerformanceManager();
  performanceManager.initializePerformanceMonitoring();

  // Preload fonts to prevent text jumping
  await _preloadFonts();

  await SharedPrefs.sharedPreferencesInitialization();

  // Initialize services in parallel for faster startup
  await ComprehensivePerformanceManager.batchOperations([
    () => MobileAds.instance.initialize(),
    () => Firebase.initializeApp(),
  ], batchName: 'Core Services Initialization');

  // Initialize ad manager early
  Get.put(PremiumAdManager(), permanent: true);

  // Initialize controllers with optimized order (most critical first)
  Get.put(AuthController(), permanent: true);
  Get.put(HeartController(), permanent: true);
  Get.put(CategoryController(), permanent: true);
  Get.put(LessonController(), permanent: true);
  Get.put(QuizController(), permanent: true);
  Get.put(AudioController(), permanent: true);
  Get.put(LeaderboardController(), permanent: true);
  Get.put(RewardedAdManagerController(), permanent: true);

  // Initialize Firebase Messaging
  FirebaseMessaging.onBackgroundMessage(_firebaseMessagingBackgroundHandler);

  // Set up foreground notification display
  FirebaseMessaging.onMessage.listen((RemoteMessage message) {
    if (message.notification != null) {
      // Use NotificationService to display notifications in the foreground if needed
      NotificationService().showNotification(
        title: message.notification!.title ?? '',
        body: message.notification!.body ?? '',
      );
    }
  });

  // Initialize remaining services in background
  ComprehensivePerformanceManager.deferToNextFrame(() async {
    tz.initializeTimeZones();
    Figma.setup(deviceWidth: 375, deviceHeight: 812);

    // Start intelligent image preloading system
    debugPrint('🚀 Lightning Image Preloader initialized');
  });

  // Optimized system UI setup
  SystemChrome.setSystemUIOverlayStyle(const SystemUiOverlayStyle(
    statusBarColor: Colors.transparent,
    statusBarBrightness: Brightness.dark,
    statusBarIconBrightness: Brightness.dark,
    systemNavigationBarColor: Colors.transparent,
    systemNavigationBarIconBrightness: Brightness.dark,
  ));

  // Enable edge-to-edge for modern UI
  SystemChrome.setEnabledSystemUIMode(SystemUiMode.edgeToEdge);

  runApp(const MyApp());
}

class MyApp extends StatelessWidget {
  const MyApp({super.key});

  @override
  Widget build(BuildContext context) {
    return GetMaterialApp(
      // Professional navigation configuration
      defaultTransition: Transition.fadeIn,
      transitionDuration: const Duration(milliseconds: 300),
      debugShowCheckedModeBanner: false,

      // Performance optimizations
      enableLog: kDebugMode,
      smartManagement: SmartManagement.keepFactory,

      // Optimized routing
      routingCallback: (routing) {
        // Performance monitoring for route changes
        if (kDebugMode && routing?.current != null) {
          debugPrint('📍 Route: ${routing!.current}');
        }
      },

      theme: ThemeData(
        primarySwatch: myCustomPrimarySwatch,
        fontFamily: 'Poppins',

        // Enable Material 3 for modern UI
        useMaterial3: true,

        // Optimize animations
        pageTransitionsTheme: const PageTransitionsTheme(
          builders: {
            TargetPlatform.android: CupertinoPageTransitionsBuilder(),
            TargetPlatform.iOS: CupertinoPageTransitionsBuilder(),
          },
        ),

        // Ensure consistent text rendering
        textTheme: const TextTheme(
          bodyLarge: TextStyle(fontFamily: 'Poppins'),
          bodyMedium: TextStyle(fontFamily: 'Poppins'),
          bodySmall: TextStyle(fontFamily: 'Poppins'),
          displayLarge: TextStyle(fontFamily: 'Poppins'),
          displayMedium: TextStyle(fontFamily: 'Poppins'),
          displaySmall: TextStyle(fontFamily: 'Poppins'),
          headlineLarge: TextStyle(fontFamily: 'Poppins'),
          headlineMedium: TextStyle(fontFamily: 'Poppins'),
          headlineSmall: TextStyle(fontFamily: 'Poppins'),
          titleLarge: TextStyle(fontFamily: 'Poppins'),
          titleMedium: TextStyle(fontFamily: 'Poppins'),
          titleSmall: TextStyle(fontFamily: 'Poppins'),
          labelLarge: TextStyle(fontFamily: 'Poppins'),
          labelMedium: TextStyle(fontFamily: 'Poppins'),
          labelSmall: TextStyle(fontFamily: 'Poppins'),
        ),

        // Ensure consistent input decoration
        inputDecorationTheme: const InputDecorationTheme(
          hintStyle: TextStyle(fontFamily: 'Poppins'),
          labelStyle: TextStyle(fontFamily: 'Poppins'),
        ),

        // Optimize splash and ripple effects
        splashFactory: InkRipple.splashFactory,
        splashColor: Colors.grey.withValues(alpha: 0.1),
        highlightColor: Colors.grey.withValues(alpha: 0.05),
      ),

      // Use professional route management
      initialRoute: AppRoutes.splash,
      getPages: AppRoutes.getPages(),

      // Performance builder with comprehensive optimizations
      builder: (context, child) {
        // Get device characteristics for optimization
        final mediaQuery = MediaQuery.of(context);
        final devicePixelRatio = mediaQuery.devicePixelRatio;
        final isLowEndDevice = devicePixelRatio < 2.0;

        return MediaQuery(
          data: mediaQuery.copyWith(
            // Ensure consistent text scaling
            textScaler: const TextScaler.linear(1.0),
            // Disable animations on low-end devices for better performance
            disableAnimations: isLowEndDevice,
            // Optimize for different screen densities
            devicePixelRatio: devicePixelRatio > 3.0 ? 3.0 : devicePixelRatio,
          ),
          child: child!,
        );
      },
    ); // Close GetMaterialApp
  }
}
