import 'dart:async';
import 'package:flutter/foundation.dart';
import 'package:flutter/scheduler.dart';

/// Advanced performance utilities for Flutter apps
class PerformanceOptimizer {
  static final Map<String, Timer?> _debounceTimers = {};
  static final Map<String, Timer?> _throttleTimers = {};
  static final Map<String, DateTime> _throttleLastRun = {};

  /// Debounce function calls - executes after delay if no new calls
  static void debounce(
    String key,
    Duration delay,
    VoidCallback callback, {
    bool callImmediately = false,
  }) {
    if (callImmediately && _debounceTimers[key] == null) {
      callback();
    }

    _debounceTimers[key]?.cancel();
    _debounceTimers[key] = Timer(delay, () {
      callback();
      _debounceTimers.remove(key);
    });
  }

  /// Throttle function calls - limits execution frequency
  static void throttle(
    String key,
    Duration interval,
    VoidCallback callback, {
    bool trailing = true,
    bool leading = true,
  }) {
    final now = DateTime.now();
    final lastRun = _throttleLastRun[key];

    void execute() {
      _throttleLastRun[key] = DateTime.now();
      callback();
    }

    if (lastRun == null || now.difference(lastRun) >= interval) {
      if (leading) {
        execute();
      }
    }

    if (trailing) {
      _throttleTimers[key]?.cancel();
      _throttleTimers[key] = Timer(interval, () {
        execute();
        _throttleTimers.remove(key);
      });
    }
  }

  /// Cancel specific debounce timer
  static void cancelDebounce(String key) {
    _debounceTimers[key]?.cancel();
    _debounceTimers.remove(key);
  }

  /// Cancel specific throttle timer
  static void cancelThrottle(String key) {
    _throttleTimers[key]?.cancel();
    _throttleTimers.remove(key);
    _throttleLastRun.remove(key);
  }

  /// Cancel all timers
  static void cancelAll() {
    _debounceTimers.forEach((key, timer) => timer?.cancel());
    _throttleTimers.forEach((key, timer) => timer?.cancel());
    _debounceTimers.clear();
    _throttleTimers.clear();
    _throttleLastRun.clear();
  }

  /// Defer heavy operations to next frame
  static Future<void> deferToNextFrame(VoidCallback callback) async {
    await SchedulerBinding.instance.endOfFrame;
    if (SchedulerBinding.instance.schedulerPhase != SchedulerPhase.idle) {
      await SchedulerBinding.instance.endOfFrame;
    }
    callback();
  }

  /// Run callback when app is idle
  static void runWhenIdle(VoidCallback callback) {
    SchedulerBinding.instance.scheduleTask(
      callback,
      Priority.idle,
    );
  }

  /// Batch multiple operations for better performance
  static Future<void> batchOperations(
    List<Future<void> Function()> operations, {
    int batchSize = 5,
    Duration delay = const Duration(milliseconds: 16),
  }) async {
    for (int i = 0; i < operations.length; i += batchSize) {
      final batch = operations.skip(i).take(batchSize);
      await Future.wait(batch.map((op) => op()));
      
      if (i + batchSize < operations.length) {
        await Future.delayed(delay);
      }
    }
  }

  /// Measure operation performance
  static Future<T> measureOperation<T>(
    String operationName,
    Future<T> Function() operation,
  ) async {
    if (!kDebugMode) {
      return operation();
    }

    final stopwatch = Stopwatch()..start();
    try {
      final result = await operation();
      stopwatch.stop();
      debugPrint('⏱️ $operationName took ${stopwatch.elapsedMilliseconds}ms');
      return result;
    } catch (e) {
      stopwatch.stop();
      debugPrint('❌ $operationName failed after ${stopwatch.elapsedMilliseconds}ms: $e');
      rethrow;
    }
  }

  /// Create a frame-aware callback that respects frame budget
  static VoidCallback createFrameCallback(
    VoidCallback callback, {
    int maxFrameTime = 16, // 16ms for 60fps
  }) {
    return () {
      final startTime = DateTime.now().millisecondsSinceEpoch;
      
      void executeCallback() {
        callback();
        
        final elapsed = DateTime.now().millisecondsSinceEpoch - startTime;
        if (elapsed > maxFrameTime && kDebugMode) {
          debugPrint('⚠️ Frame callback exceeded budget: ${elapsed}ms');
        }
      }

      if (SchedulerBinding.instance.schedulerPhase == SchedulerPhase.idle) {
        executeCallback();
      } else {
        SchedulerBinding.instance.addPostFrameCallback((_) => executeCallback());
      }
    };
  }
}

/// Smart scroll performance optimizer
class ScrollPerformanceOptimizer {
  final Duration scrollEndDelay;
  final VoidCallback? onScrollStart;
  final VoidCallback? onScrollEnd;
  final VoidCallback? onScrollUpdate;
  
  Timer? _scrollEndTimer;
  bool _isScrolling = false;
  double _lastScrollPosition = 0;
  
  ScrollPerformanceOptimizer({
    this.scrollEndDelay = const Duration(milliseconds: 150),
    this.onScrollStart,
    this.onScrollEnd,
    this.onScrollUpdate,
  });

  void handleScroll(double scrollPosition) {
    if (!_isScrolling) {
      _isScrolling = true;
      onScrollStart?.call();
    }

    _lastScrollPosition = scrollPosition;
    
    // Throttle scroll updates
    PerformanceOptimizer.throttle(
      'scroll_update',
      const Duration(milliseconds: 16), // 60fps
      () => onScrollUpdate?.call(),
    );

    // Detect scroll end
    _scrollEndTimer?.cancel();
    _scrollEndTimer = Timer(scrollEndDelay, () {
      _isScrolling = false;
      onScrollEnd?.call();
    });
  }

  double get lastScrollPosition => _lastScrollPosition;
  bool get isScrolling => _isScrolling;

  void dispose() {
    _scrollEndTimer?.cancel();
    PerformanceOptimizer.cancelThrottle('scroll_update');
  }
}

/// Image loading performance optimizer
class ImageLoadingOptimizer {
  static final Map<String, bool> _loadingImages = {};
  static const int _maxConcurrentLoads = 3;

  static Future<void> loadImageWithPriority(
    String url,
    VoidCallback onLoad, {
    int priority = 0,
  }) async {
    // Wait if too many images are loading
    while (_loadingImages.length >= _maxConcurrentLoads) {
      await Future.delayed(const Duration(milliseconds: 50));
    }

    _loadingImages[url] = true;
    
    try {
      // Simulate image loading with priority
      await Future.delayed(
        Duration(milliseconds: 100 + (priority * 50)),
      );
      
      onLoad();
    } finally {
      _loadingImages.remove(url);
    }
  }

  static bool isLoading(String url) => _loadingImages[url] ?? false;
  
  static int get activeLoads => _loadingImages.length;
}

/// Memory-aware cache manager
class MemoryAwareCacheManager<K, V> {
  final int maxSize;
  final Map<K, V> _cache = {};
  final List<K> _accessOrder = [];

  MemoryAwareCacheManager({this.maxSize = 50});

  V? get(K key) {
    if (_cache.containsKey(key)) {
      // Move to end (most recently used)
      _accessOrder.remove(key);
      _accessOrder.add(key);
      return _cache[key];
    }
    return null;
  }

  void put(K key, V value) {
    if (_cache.containsKey(key)) {
      _accessOrder.remove(key);
    } else if (_cache.length >= maxSize) {
      // Remove least recently used
      final lru = _accessOrder.removeAt(0);
      _cache.remove(lru);
    }

    _cache[key] = value;
    _accessOrder.add(key);
  }

  void clear() {
    _cache.clear();
    _accessOrder.clear();
  }

  int get size => _cache.length;
}
