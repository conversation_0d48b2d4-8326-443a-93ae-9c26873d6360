import 'package:bibl/controllers/category_controller.dart';
import 'package:bibl/controllers/profile_controller.dart';
import 'package:flutter/material.dart';
import 'package:flutter_svg/svg.dart';
import 'package:get/get.dart';
import '../models/category_model.dart';
import '../res/style.dart';
import 'category_icon_widget.dart';
import 'manage_interest_bsheet.dart';

void showManageInterestsBottomSheet(BuildContext context, bool isFromLibrary) {
  // Dismiss keyboard before showing bottom sheet
  FocusScope.of(context).unfocus();
  
  showModalBottomSheet(
    context: context,
    enableDrag: true,
    isScrollControlled: true,
    backgroundColor: Colors.transparent,
    // Prevent keyboard from appearing
    isDismissible: true,
    // Don't resize for keyboard
    useSafeArea: true,
    builder: (context) => ManageInterestsBottomSheet(
      isFromLibrary: isFromLibrary,
    ),
  ).then((_) {
    // Ensure keyboard stays dismissed after closing
    FocusScope.of(context).unfocus();
  });
}

void showBottomSheetWidget(BuildContext context, {required Widget child}) {
  // Dismiss keyboard before showing any bottom sheet
  FocusScope.of(context).unfocus();
  
  showModalBottomSheet(
    context: context,
    enableDrag: true,
    isScrollControlled: true,
    backgroundColor: Colors.transparent,
    // Prevent keyboard from appearing
    isDismissible: true,
    useSafeArea: true,
    builder: (context) => child,
  ).then((_) {
    // Ensure keyboard stays dismissed after closing
    FocusScope.of(context).unfocus();
  });
}

// Define a list of colors
final List<Color> containerColors = [
  const Color(0xffF8EFFF),
  Colors.transparent,
  const Color(0xffFFEFEF),
  const Color(0xffD3FFE0),
];

// Function to return the Row widget with dynamic colors
Row articleTitleWidget(
    {required String image, required String title, required int index}) {
  // Use modulo to repeat the colors based on index
  Color currentColor = containerColors[index % containerColors.length];

  return Row(
    children: [
      Container(
        decoration: BoxDecoration(
          color: currentColor, // Set color based on index
          border: Border.all(color: Colors.black.withValues(alpha: 0.2)),
          borderRadius: BorderRadius.circular(12),
        ),
        child: Padding(
          padding: const EdgeInsets.all(5.0),
          child: Center(
            child: Row(
              children: [
                CustomCategoryIcon(
                  topicName: title,
                  topicPhotoLink: image,
                  isSelected: false,
                ),
                Txt(
                  txt: title,
                  fontSize: 14,
                  fontWeight: FontWeight.normal,
                )
              ],
            ),
          ),
        ),
      ),
      const SizedBox(
        width: 10,
      )
    ],
  );
}

List<CategoryModel> _getFilteredCategories(
  bool isFromLibrary,
  ProfileController profileController,
  CategoryController categoryController,
) {
  // Get the list of user's selected categories
  List<String> userSelectedCategories = isFromLibrary
      ? profileController.userr.value.listOfLibraryCategories ?? []
      : profileController.userr.value.listOfFavCategories ?? [];

  // Filter categories by matching with allCategories
  return categoryController.allCategories
      .where((category) => userSelectedCategories.contains(category.topicName))
      .toList();
}

Widget interestsWidget(BuildContext context, bool isFromLibrary) {
  final ProfileController profileController = Get.find();
  final CategoryController categoryController = Get.find();

  return Obx(() {
    // Fetch the filtered list of categories (reactive)
    final List<CategoryModel> filteredCategories = _getFilteredCategories(
      isFromLibrary,
      profileController,
      categoryController,
    );

    return AnimatedOpacity(
      opacity: filteredCategories.isNotEmpty ? 1.0 : 0.0,
      duration: const Duration(milliseconds: 500),
      child: Column(
        children: [
          Padding(
            padding: EdgeInsets.fromLTRB(16, isFromLibrary ? 0 : 16, 16, 16),
            child: Row(
              children: [
                const Expanded(
                  child: Txt(txt: 'Zainteresovan si za', fontSize: 16),
                ),
                IconButton(
                  onPressed: () {
                    // Ensure any keyboard is dismissed before opening bottom sheet
                    FocusScope.of(context).unfocus();
                    Future.delayed(const Duration(milliseconds: 100), () {
                      showManageInterestsBottomSheet(context, isFromLibrary);
                    });
                  },
                  icon: SvgPicture.asset('assets/svgs/filter.svg'),
                ),
              ],
            ),
          ),
          Padding(
            padding: const EdgeInsets.fromLTRB(16, 0, 0, 8),
            child: SizedBox(
              height: 32,
              child: ListView.builder(
                itemCount: filteredCategories.length,
                cacheExtent: 100,
                physics: const BouncingScrollPhysics(),
                scrollDirection: Axis.horizontal,
                itemBuilder: (context, index) {
                  final category = filteredCategories[index];
                  return TweenAnimationBuilder<double>(
                    tween: Tween(begin: 0.0, end: 1.0),
                    duration: Duration(milliseconds: 300 + (index * 100)),
                    curve: Curves.easeOut,
                    builder: (context, value, child) {
                      return Transform.translate(
                        offset: Offset((1 - value) * 30, 0),
                        child: Opacity(
                          opacity: value,
                          child: articleTitleWidget(
                            image: category.topicPhotoLink ?? '',
                            title: category.topicName ?? '',
                            index: index,
                          ),
                        ),
                      );
                    },
                  );
                },
              ),
            ),
          ),
        ],
      ),
    );
  });
}
