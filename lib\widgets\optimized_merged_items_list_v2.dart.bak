import 'dart:async';
import 'dart:io';
import 'package:bibl/controllers/lesson_controller.dart';
import 'package:bibl/controllers/profile_controller.dart';
import 'package:bibl/models/lesson_model.dart';
import 'package:bibl/models/quiz_model.dart';
import 'package:bibl/res/style.dart';
import 'package:bibl/widgets/box_widget.dart';
import 'package:bibl/widgets/premium_skeleton_loader.dart';
import 'package:bibl/utils/premium_animations.dart';
import 'package:bibl/utils/lightning_image_preloader.dart';
import 'interests_widget.dart';
import 'package:flutter/material.dart';
import 'package:flutter/rendering.dart';
import 'package:get/get.dart';
import 'package:google_mobile_ads/google_mobile_ads.dart';

/// Ultra-optimized MergedItemsList with premium performance and zero layout shifts
class OptimizedMergedItemsList extends StatefulWidget {
  final bool isForLibrary;
  final ScrollController scrollController;

  const OptimizedMergedItemsList({
    Key? key,
    required this.isForLibrary,
    required this.scrollController,
  }) : super(key: key);

  @override
  State<OptimizedMergedItemsList> createState() =>
      _OptimizedMergedItemsListState();
}

class _OptimizedMergedItemsListState extends State<OptimizedMergedItemsList>
    with TickerProviderStateMixin, AutomaticKeepAliveClientMixin {
  final ProfileController profileController = Get.find();
  final LessonController lessonController = Get.find();

  // Enhanced ad management with permanent caching
  final Map<int, BannerAd> _adCache = {};
  final Map<int, bool> _adLoadingStatus = {};
  final Set<int> _failedAdIndices = {};
  
  // Performance optimizations
  Timer? _scrollDebounceTimer;
  Timer? _imagePreloadTimer;
  bool _isLoadingMore = false;
  
  // FAB management
  bool _showScrollToTopButton = false;
  late AnimationController _fabAnimationController;
  late Animation<double> _fabScaleAnimation;
  
  // Constants for optimal performance
  static const double _adHeight = 300.0; // Fixed ad height to prevent shifts
  static const int _maxConcurrentAds = 5;
  static const int _preloadAheadCount = 3;
  static const Duration _scrollDebounceDelay = Duration(milliseconds: 100);
  static const Duration _imagePreloadDelay = Duration(milliseconds: 300);

  @override
  void initState() {
    super.initState();
    _initializeAnimations();
    _setupScrollListener();
    
    // Preload initial ads immediately for instant display
    WidgetsBinding.instance.addPostFrameCallback((_) {
      if (!_isPremiumUser) {
        _preloadInitialAds();
      }
    });
  }

  bool get _isPremiumUser => profileController.userr.value.isPremiumUser ?? false;

  void _initializeAnimations() {
    _fabAnimationController = AnimationController(
      duration: const Duration(milliseconds: 300),
      vsync: this,
    );

    _fabScaleAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _fabAnimationController,
      curve: Curves.elasticOut,
    ));
  }

  void _setupScrollListener() {
    widget.scrollController.addListener(_onScroll);
  }

  void _preloadInitialAds() {
    // Preload first 5 ads for instant display
    final adPositions = [3, 7, 11, 15, 19];
    for (final position in adPositions) {
      _ensureAdLoaded(position);
    }
  }

  void _onScroll() {
    // Debounce scroll events for better performance
    _scrollDebounceTimer?.cancel();
    _scrollDebounceTimer = Timer(_scrollDebounceDelay, () {
      if (!mounted) return;
      
      _handleScrollUpdate();
    });
  }

  void _handleScrollUpdate() {
    final offset = widget.scrollController.offset;
    final maxScrollExtent = widget.scrollController.position.maxScrollExtent;

    // Update FAB visibility
    final shouldShowFAB = offset > 300;
    if (shouldShowFAB != _showScrollToTopButton) {
      setState(() {
        _showScrollToTopButton = shouldShowFAB;
      });

      if (shouldShowFAB) {
        _fabAnimationController.forward();
      } else {
        _fabAnimationController.reverse();
      }
    }

    // Load more content when near bottom
    if (maxScrollExtent > 0 && 
        offset >= maxScrollExtent - 1000 && 
        !_isLoadingMore) {
      _loadMoreContent();
    }

    // Debounced image preloading
    _imagePreloadTimer?.cancel();
    _imagePreloadTimer = Timer(_imagePreloadDelay, () {
      if (mounted) {
        _intelligentImagePreload();
      }
    });

    // Preload upcoming ads
    if (!_isPremiumUser) {
      _preloadUpcomingAds(offset);
    }
  }

  void _intelligentImagePreload() {
    try {
      final scrollOffset = widget.scrollController.offset;
      final viewportHeight = MediaQuery.of(context).size.height;
      final itemHeight = 220.0; // Approximate item height
      
      final firstVisibleIndex = (scrollOffset / itemHeight).floor();
      final lastVisibleIndex = ((scrollOffset + viewportHeight) / itemHeight).ceil();
      
      final items = _getItems();
      final visibleUrls = <String>[];
      final upcomingUrls = <String>[];
      
      // Collect visible item URLs
      for (int i = firstVisibleIndex; i <= lastVisibleIndex && i < items.length; i++) {
        final url = _getImageUrl(items[i]);
        if (url != null) visibleUrls.add(url);
      }
      
      // Collect upcoming item URLs (next 10 items)
      for (int i = lastVisibleIndex + 1; 
           i < items.length && i <= lastVisibleIndex + 10; 
           i++) {
        final url = _getImageUrl(items[i]);
        if (url != null) upcomingUrls.add(url);
      }
      
      // Smart preload with priority
      if (visibleUrls.isNotEmpty || upcomingUrls.isNotEmpty) {
        LightningImagePreloader.smartPreload(visibleUrls, upcomingUrls);
      }
    } catch (e) {
      debugPrint('Error in image preloading: $e');
    }
  }

  void _preloadUpcomingAds(double scrollOffset) {
    final itemHeight = 260.0; // Item + ad height
    final currentIndex = (scrollOffset / itemHeight).floor();
    
    // Preload next 3 ad positions
    for (int i = 1; i <= _preloadAheadCount; i++) {
      final nextAdPosition = ((currentIndex + i * 3) ~/ 4) * 4 + 3;
      if (nextAdPosition < _getTotalChildCount()) {
        _ensureAdLoaded(nextAdPosition);
      }
    }
  }

  String? _getImageUrl(dynamic item) {
    if (item is LessonModel) return item.imageLink;
    if (item is QuizModel) return item.quizImageLink;
    return null;
  }

  Future<void> _loadMoreContent() async {
    if (_isLoadingMore) return;

    setState(() {
      _isLoadingMore = true;
    });

    try {
      // Load more items
      if (widget.isForLibrary) {
        lessonController.loadMoreLibraryDisplayItems();
      } else {
        lessonController.loadMoreHomeDisplayItems();
      }

      // Small delay for smooth UX
      await Future.delayed(const Duration(milliseconds: 200));
    } finally {
      if (mounted) {
        setState(() {
          _isLoadingMore = false;
        });
      }
    }
  }

  void _ensureAdLoaded(int position) {
    if (_failedAdIndices.contains(position) || 
        _adCache.containsKey(position) ||
        _adLoadingStatus[position] == true) {
      return;
    }

    if (_adCache.length >= _maxConcurrentAds) {
      // Don't load more ads if we've reached the limit
      return;
    }

    _loadBannerAd(position);
  }

  Future<void> _loadBannerAd(int position) async {
    if (_adLoadingStatus[position] == true) return;
    
    _adLoadingStatus[position] = true;

    try {
      final adUnitId = _getAdUnitId(position);
      
      final ad = BannerAd(
        adUnitId: adUnitId,
        size: AdSize(
          width: (Get.width - 40).toInt(),
          height: (_adHeight - 60).toInt(), // Account for padding and label
        ),
        request: const AdRequest(
          requestAgent: 'flutter-webview',
          nonPersonalizedAds: false,
        ),
        listener: BannerAdListener(
          onAdLoaded: (ad) {
            if (mounted) {
              setState(() {
                _adCache[position] = ad as BannerAd;
                _adLoadingStatus[position] = false;
              });
            } else {
              ad.dispose();
            }
          },
          onAdFailedToLoad: (ad, error) {
            debugPrint('Ad failed to load at position $position: ${error.message}');
            ad.dispose();
            if (mounted) {
              setState(() {
                _failedAdIndices.add(position);
                _adLoadingStatus[position] = false;
              });
            }
          },
          onAdOpened: (ad) {
            debugPrint('Ad opened at position $position');
          },
          onAdClosed: (ad) {
            debugPrint('Ad closed at position $position');
          },
        ),
      );

      await ad.load();
    } catch (e) {
      debugPrint('Error loading ad at position $position: $e');
      if (mounted) {
        setState(() {
          _failedAdIndices.add(position);
          _adLoadingStatus[position] = false;
        });
      }
    }
  }

  String _getAdUnitId(int position) {
    final adUnitMap = Platform.isAndroid
        ? {
            3: 'ca-app-pub-8639821055582439/1096152024',
            7: 'ca-app-pub-8639821055582439/9477204925',
            11: 'ca-app-pub-8639821055582439/4156563660',
            -1: 'ca-app-pub-8639821055582439/8056296139',
          }
        : {
            3: 'ca-app-pub-8639821055582439/3769550858',
            7: 'ca-app-pub-8639821055582439/4265224933',
            11: 'ca-app-pub-8639821055582439/1746888714',
            -1: 'ca-app-pub-8639821055582439/2021354915',
          };

    return adUnitMap[position % 12] ?? adUnitMap[-1]!;
  }

  Widget _buildOptimizedAd(int position) {
    return RepaintBoundary(
      child: Container(
        margin: const EdgeInsets.fromLTRB(16, 10, 16, 20),
        height: _adHeight, // Fixed height prevents layout shifts
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(16),
          boxShadow: [
            BoxShadow(
              color: Colors.black.withValues(alpha: 0.08),
              blurRadius: 24,
              offset: const Offset(0, 4),
            ),
          ],
        ),
        child: Column(
          children: [
            // Ad label
            Container(
              height: 32,
              alignment: Alignment.center,
              child: Container(
                padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 4),
                decoration: BoxDecoration(
                  color: Colors.grey[100],
                  borderRadius: BorderRadius.circular(12),
                ),
                child: const Text(
                  'REKLAMA',
                  style: TextStyle(
                    fontSize: 11,
                    fontWeight: FontWeight.w600,
                    color: Colors.grey,
                    letterSpacing: 0.5,
                  ),
                ),
              ),
            ),
            // Ad content with fixed container
            Expanded(
              child: Container(
                margin: const EdgeInsets.all(8),
                decoration: BoxDecoration(
                  color: Colors.grey[50],
                  borderRadius: BorderRadius.circular(12),
                ),
                child: ClipRRect(
                  borderRadius: BorderRadius.circular(12),
                  child: _buildAdContent(position),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildAdContent(int position) {
    final ad = _adCache[position];
    final isLoading = _adLoadingStatus[position] == true;
    final hasFailed = _failedAdIndices.contains(position);

    if (hasFailed) {
      // Show placeholder for failed ads
      return Container(
        color: Colors.grey[100],
        child: const Center(
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Icon(Icons.ads_click, size: 32, color: Colors.grey),
              SizedBox(height: 8),
              Text(
                'Obsah není k dispozici',
                style: TextStyle(color: Colors.grey, fontSize: 12),
              ),
            ],
          ),
        ),
      );
    }

    if (ad != null) {
      // Ad is loaded, show it with fade-in animation
      return AnimatedOpacity(
        opacity: 1.0,
        duration: const Duration(milliseconds: 300),
        child: AdWidget(ad: ad),
      );
    }

    // Start loading if not already loading
    if (!isLoading && !hasFailed) {
      _ensureAdLoaded(position);
    }

    // Show optimized loading placeholder
    return Container(
      color: Colors.grey[50],
      child: Center(
        child: SizedBox(
          width: 20,
          height: 20,
          child: CircularProgressIndicator(
            strokeWidth: 2.0,
            valueColor: AlwaysStoppedAnimation<Color>(Colors.grey[400]!),
          ),
        ),
      ),
    );
  }

  bool _isAdIndex(int index) {
    return (index + 1) % 4 == 0; // Show ad every 4th position
  }

  int _calculateItemIndex(int visualIndex) {
    if (_isPremiumUser) return visualIndex;
    
    // Calculate actual item index accounting for ads
    return visualIndex - (visualIndex ~/ 4);
  }

  int _getTotalChildCount() {
    final itemCount = _getItemCount();
    if (_isPremiumUser) return itemCount;
    
    // Add ad positions
    return itemCount + (itemCount ~/ 3);
  }

  int _getItemCount() {
    return widget.isForLibrary
        ? lessonController.libraryDisplayedItems.length
        : lessonController.homeDisplayedItems.length;
  }

  List<dynamic> _getItems() {
    return widget.isForLibrary
        ? lessonController.libraryDisplayedItems
        : lessonController.homeDisplayedItems;
  }

  Widget _buildOptimizedItem(int index) {
    final items = _getItems();
    if (index >= items.length) {
      return const SizedBox.shrink();
    }

    final item = items[index];

    return RepaintBoundary(
      key: ValueKey('item_${item.hashCode}'),
      child: Padding(
        padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
        child: BoxWidget(
          lesson: item is LessonModel ? item : null,
          quiz: item is QuizModel ? item : null,
          shuffleQuiz: item is ShuffleQuizModel ? item : null,
        ),
      ),
    );
  }

  void _scrollToTop() {
    widget.scrollController.animateTo(
      0,
      duration: const Duration(milliseconds: 800),
      curve: Curves.easeOutCubic,
    );
  }

  Future<void> _onRefresh() async {
    // Clear ad cache on refresh
    if (!_isPremiumUser) {
      for (final ad in _adCache.values) {
        ad.dispose();
      }
      _adCache.clear();
      _adLoadingStatus.clear();
      _failedAdIndices.clear();
    }

    // Refresh content
    if (widget.isForLibrary) {
      lessonController.shuffleAllItems(
        isShuffle: true,
        shouldClear: true,
        from: 'optimized list library refresh',
      );
    } else {
      lessonController.mergeAndShuffleItems(
        isShuffle: true,
        from: 'optimized list home refresh',
        shouldClear: true,
      );
    }

    // Preload new ads
    if (!_isPremiumUser) {
      await Future.delayed(const Duration(milliseconds: 300));
      _preloadInitialAds();
    }
  }

  @override
  Widget build(BuildContext context) {
    super.build(context);

    return Obx(() {
      final items = _getItems();
      
      // Show skeleton loader while loading
      if (items.isEmpty && profileController.isUserDataLoading.value) {
        return const PremiumSkeletonLoader.list(itemCount: 5);
      }

      return Stack(
        children: [
          // Main content
          RefreshIndicator(
            color: mainColor,
            onRefresh: _onRefresh,
            child: CustomScrollView(
              controller: widget.scrollController,
              physics: const BouncingScrollPhysics(
                parent: AlwaysScrollableScrollPhysics(),
              ),
              cacheExtent: 500, // Increase cache for smoother scrolling
              slivers: [
                // Interests header
                SliverToBoxAdapter(
                  child: Column(
                    children: [
                      interestsWidget(context, widget.isForLibrary),
                      const SizedBox(height: 20),
                    ],
                  ),
                ),

                // Main content list with optimized builder
                SliverList(
                  delegate: SliverChildBuilderDelegate(
                    (context, index) {
                      // Check if this is an ad position
                      if (!_isPremiumUser && _isAdIndex(index)) {
                        return _buildOptimizedAd(index);
                      }

                      // Calculate actual item index
                      final itemIndex = _calculateItemIndex(index);
                      if (itemIndex >= _getItemCount()) {
                        return const SizedBox.shrink();
                      }

                      return _buildOptimizedItem(itemIndex);
                    },
                    childCount: _getTotalChildCount(),
                    addAutomaticKeepAlives: true,
                    addRepaintBoundaries: true,
                  ),
                ),

                // Loading indicator
                if (_isLoadingMore)
                  const SliverToBoxAdapter(
                    child: Padding(
                      padding: EdgeInsets.all(20),
                      child: Center(
                        child: PremiumLoadingIndicator(),
                      ),
                    ),
                  ),

                // Bottom padding
                const SliverPadding(
                  padding: EdgeInsets.only(bottom: 100),
                ),
              ],
            ),
          ),

          // Scroll to top FAB
          if (_showScrollToTopButton)
            Positioned(
              right: 16,
              bottom: 16,
              child: ScaleTransition(
                scale: _fabScaleAnimation,
                child: FloatingActionButton(
                  onPressed: _scrollToTop,
                  backgroundColor: mainColor,
                  child: const Icon(
                    Icons.keyboard_arrow_up,
                    color: Colors.white,
                    size: 28,
                  ),
                ),
              ),
            ),
        ],
      );
    });
  }

  @override
  bool get wantKeepAlive => true;

  @override
  void dispose() {
    // Cancel timers
    _scrollDebounceTimer?.cancel();
    _imagePreloadTimer?.cancel();
    
    // Remove scroll listener
    widget.scrollController.removeListener(_onScroll);
    
    // Dispose animations
    _fabAnimationController.dispose();

    // Dispose ads
    for (final ad in _adCache.values) {
      ad.dispose();
    }
    _adCache.clear();

    super.dispose();
  }
}
